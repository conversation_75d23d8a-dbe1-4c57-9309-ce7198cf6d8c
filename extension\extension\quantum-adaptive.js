/**
 * KOS Quantum Adaptive Bot
 * Advanced adaptive position sizing with anti-martingale progression
 */

// Global variables
let isActive = false;
let isPaused = false;
let currentBalance = 0;
let initialBalance = 0;
let riskCapital = 50; // Percentage
let profitTarget = 10; // Percentage
let quantumMultiplier = 2.087;
let maxSequence = 5;
let currentSequence = 0;
let consecutiveLosses = 0;
let totalTrades = 0;
let winningTrades = 0;
let losingTrades = 0;
let trades = [];
let tradingInterval;

// DOM elements
const activateBtn = document.getElementById('activateBtn');
const pauseBtn = document.getElementById('pauseBtn');
const riskCapitalInput = document.getElementById('riskCapital');
const profitTargetInput = document.getElementById('profitTarget');
const quantumMultiplierInput = document.getElementById('quantumMultiplier');
const maxSequenceInput = document.getElementById('maxSequence');

// Status elements
const currentStateElement = document.getElementById('currentState');
const nextActionElement = document.getElementById('nextAction');
const tradeAmountElement = document.getElementById('tradeAmount');

// Stats elements
const totalTradesElement = document.getElementById('totalTrades');
const winRateElement = document.getElementById('winRate');
const currentProfitElement = document.getElementById('currentProfit');
const currentBalanceElement = document.getElementById('currentBalance');

// Trades list
const tradesListElement = document.getElementById('tradesList');

// Quantum metrics
const coherenceElement = document.getElementById('coherenceValue');
const entanglementElement = document.getElementById('entanglementValue');
const probabilityElement = document.getElementById('probabilityValue');

// Initialize the interface
function initializeInterface() {
    console.log('Initializing Quantum Adaptive Bot interface');

    // Set up event listeners
    activateBtn.addEventListener('click', toggleActivation);
    pauseBtn.addEventListener('click', togglePause);

    // Configuration inputs
    riskCapitalInput.addEventListener('change', updateRiskCapital);
    profitTargetInput.addEventListener('change', updateProfitTarget);
    quantumMultiplierInput.addEventListener('change', updateQuantumMultiplier);
    maxSequenceInput.addEventListener('change', updateMaxSequence);

    // Initialize quantum metrics animation
    animateQuantumMetrics();

    // Update initial state
    updateStatus();
    updateStats();

    // Listen for messages from parent
    window.addEventListener('message', handleMessage);

    console.log('Quantum Adaptive Bot interface initialized');
}

// Handle messages from parent window
function handleMessage(event) {
    const { action, data } = event.data;

    switch (action) {
        case 'updateBalance':
            currentBalance = data.balance;
            if (initialBalance === 0) {
                initialBalance = currentBalance;
            }
            updateStats();
            break;

        case 'tradeResult':
            handleTradeResult(data);
            break;

        case 'forceHeightUpdate':
            // Force height update if needed
            break;

        default:
            console.log('Unknown message action:', action);
    }
}

// Toggle activation
function toggleActivation() {
    isActive = !isActive;

    if (isActive) {
        startTrading();
    } else {
        stopTrading();
    }

    updateButtons();
    updateStatus();
}

// Toggle pause
function togglePause() {
    isPaused = !isPaused;
    updateButtons();
    updateStatus();

    if (isPaused) {
        clearInterval(tradingInterval);
    } else if (isActive) {
        startTradingCycle();
    }
}

// Start trading
function startTrading() {
    console.log('Starting Quantum Adaptive trading');
    currentSequence = 0;
    consecutiveLosses = 0;
    startTradingCycle();
}

// Stop trading
function stopTrading() {
    console.log('Stopping Quantum Adaptive trading');
    clearInterval(tradingInterval);
}

// Start trading cycle
function startTradingCycle() {
    if (!isActive || isPaused) return;

    // Calculate next trade amount
    const tradeAmount = calculateAdaptivePosition();
    
    // Update UI
    updateTradeAmount(tradeAmount);
    
    // Simulate quantum analysis delay
    setTimeout(() => {
        if (isActive && !isPaused) {
            executeQuantumTrade(tradeAmount);
        }
    }, 2000 + Math.random() * 3000); // 2-5 second delay
}

// Calculate adaptive position size
function calculateAdaptivePosition() {
    const riskAmount = (currentBalance * riskCapital) / 100;
    
    if (consecutiveLosses === 0) {
        // Base position
        return Math.round((riskAmount * 0.02) * 100) / 100; // 2% of risk capital
    } else {
        // Adaptive progression
        const multiplier = Math.pow(quantumMultiplier, consecutiveLosses);
        const adaptiveAmount = (riskAmount * 0.02) * multiplier;
        
        // Cap at maximum sequence
        if (consecutiveLosses >= maxSequence) {
            return Math.round((riskAmount * 0.1) * 100) / 100; // 10% of risk capital max
        }
        
        return Math.round(adaptiveAmount * 100) / 100;
    }
}

// Execute quantum trade
function executeQuantumTrade(amount) {
    const direction = Math.random() > 0.5 ? 'CALL' : 'PUT';
    
    console.log(`Executing quantum trade: ${direction} for $${amount}`);
    
    // Update status
    currentStateElement.textContent = `Executing ${direction} trade`;
    nextActionElement.textContent = `Trade amount: $${amount}`;
    
    // Send trade to parent
    window.parent.postMessage({
        action: 'executeTrade',
        data: {
            direction: direction.toLowerCase(),
            amount: amount,
            mode: 'quantum'
        }
    }, '*');
    
    // Simulate trade execution
    setTimeout(() => {
        simulateTradeResult(direction, amount);
    }, 5000); // 5 second trade duration
}

// Simulate trade result (for testing)
function simulateTradeResult(direction, amount) {
    const winRate = 0.80; // 80% win rate for quantum mode
    const isWin = Math.random() < winRate;
    
    const result = {
        direction,
        amount,
        result: isWin ? 'win' : 'loss',
        profit: isWin ? amount * 0.85 : -amount, // 85% payout
        timestamp: new Date()
    };
    
    handleTradeResult(result);
}

// Handle trade result
function handleTradeResult(result) {
    console.log('Trade result:', result);
    
    totalTrades++;
    
    if (result.result === 'win') {
        winningTrades++;
        consecutiveLosses = 0; // Reset on win
        currentBalance += result.profit;
    } else {
        losingTrades++;
        consecutiveLosses++;
        currentBalance += result.profit; // Profit is negative for losses
    }
    
    // Add to trades history
    trades.unshift(result);
    if (trades.length > 10) {
        trades.pop(); // Keep only last 10 trades
    }
    
    // Update UI
    updateStats();
    updateTradesHistory();
    
    // Check profit target
    const currentProfit = currentBalance - initialBalance;
    const profitPercentage = (currentProfit / initialBalance) * 100;
    
    if (profitPercentage >= profitTarget) {
        showCongratulations(currentProfit);
        stopTrading();
        return;
    }
    
    // Check if we should continue trading
    if (isActive && !isPaused && consecutiveLosses < maxSequence) {
        currentSequence++;
        setTimeout(startTradingCycle, 3000); // 3 second delay between trades
    } else if (consecutiveLosses >= maxSequence) {
        // Reset sequence after max losses
        consecutiveLosses = 0;
        currentSequence = 0;
        setTimeout(startTradingCycle, 5000); // 5 second delay after reset
    }
}

// Update configuration
function updateRiskCapital() {
    riskCapital = parseFloat(riskCapitalInput.value);
}

function updateProfitTarget() {
    profitTarget = parseFloat(profitTargetInput.value);
}

function updateQuantumMultiplier() {
    quantumMultiplier = parseFloat(quantumMultiplierInput.value);
}

function updateMaxSequence() {
    maxSequence = parseInt(maxSequenceInput.value);
}

// Update UI functions
function updateButtons() {
    if (isActive) {
        activateBtn.innerHTML = '<i class="fas fa-stop"></i><span>Deactivate</span>';
        activateBtn.style.background = 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)';
        pauseBtn.disabled = false;
    } else {
        activateBtn.innerHTML = '<i class="fas fa-play"></i><span>Activate Quantum Field</span>';
        activateBtn.style.background = 'linear-gradient(135deg, #4a90e2 0%, #357abd 100%)';
        pauseBtn.disabled = true;
    }
    
    if (isPaused) {
        pauseBtn.innerHTML = '<i class="fas fa-play"></i><span>Resume</span>';
    } else {
        pauseBtn.innerHTML = '<i class="fas fa-pause"></i><span>Pause</span>';
    }
}

function updateStatus() {
    if (!isActive) {
        currentStateElement.textContent = 'Quantum field ready';
        nextActionElement.textContent = 'Awaiting activation';
    } else if (isPaused) {
        currentStateElement.textContent = 'Trading paused';
        nextActionElement.textContent = 'Click resume to continue';
    } else {
        currentStateElement.textContent = 'Quantum field active';
        nextActionElement.textContent = 'Analyzing market patterns';
    }
}

function updateTradeAmount(amount) {
    tradeAmountElement.textContent = `$${amount.toFixed(2)}`;
}

function updateStats() {
    totalTradesElement.textContent = totalTrades;
    
    const winRate = totalTrades > 0 ? ((winningTrades / totalTrades) * 100).toFixed(1) : 0;
    winRateElement.textContent = `${winRate}%`;
    
    const currentProfit = currentBalance - initialBalance;
    currentProfitElement.textContent = `$${currentProfit.toFixed(2)}`;
    currentProfitElement.style.color = currentProfit >= 0 ? '#28a745' : '#dc3545';
    
    currentBalanceElement.textContent = `$${currentBalance.toFixed(2)}`;
}

function updateTradesHistory() {
    if (trades.length === 0) {
        tradesListElement.innerHTML = '<div class="no-trades">No trades executed yet</div>';
        return;
    }
    
    const tradesHTML = trades.map(trade => {
        const time = trade.timestamp.toLocaleTimeString();
        const profitClass = trade.result === 'win' ? 'positive' : 'negative';
        
        return `
            <div class="trade-item ${trade.result}">
                <div class="trade-info">
                    <div class="trade-direction">${trade.direction.toUpperCase()}</div>
                    <div class="trade-time">${time}</div>
                </div>
                <div class="trade-result">
                    <div class="trade-amount">$${trade.amount.toFixed(2)}</div>
                    <div class="trade-profit ${profitClass}">${trade.profit >= 0 ? '+' : ''}$${trade.profit.toFixed(2)}</div>
                </div>
            </div>
        `;
    }).join('');
    
    tradesListElement.innerHTML = tradesHTML;
}

// Animate quantum metrics
function animateQuantumMetrics() {
    setInterval(() => {
        // Simulate quantum fluctuations
        const coherence = 85 + Math.random() * 10;
        const entanglement = 90 + Math.random() * 8;
        const probability = 75 + Math.random() * 15;
        
        coherenceElement.textContent = `${coherence.toFixed(0)}%`;
        entanglementElement.textContent = `${entanglement.toFixed(0)}%`;
        probabilityElement.textContent = `${probability.toFixed(0)}%`;
    }, 2000);
}

// Show congratulations
function showCongratulations(profit) {
    alert(`🎉 Congratulations! You've reached your profit target!\n\nProfit made: $${profit.toFixed(2)}\n\nThe quantum field has been deactivated.`);
    isActive = false;
    updateButtons();
    updateStatus();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeInterface);
