<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binary Options Trading Bot</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4286f4;
            --secondary-color: #ff6384;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --dark-bg: #0f0f1a;
            --medium-bg: #1a1a2e;
            --light-bg: #28293d;
            --text-light: #ffffff;
            --text-medium: #d1d5db;
            --text-dark: #9ca3af;
            --border-color: rgba(66, 134, 244, 0.2);
            --pocket-option-blue: #0056b3;
            --pocket-option-dark: #1e2a3b;
            --pocket-option-light: #2c3e50;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, var(--pocket-option-dark) 0%, var(--dark-bg) 100%);
            color: var(--text-light);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            flex: 1;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background-color: var(--pocket-option-dark);
            border-bottom: 2px solid var(--primary-color);
        }

        h1 {
            margin: 0;
            font-size: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
        }

        h1 i {
            margin-right: 15px;
            font-size: 32px;
            color: var(--primary-color);
        }

        .subtitle {
            margin-top: 10px;
            color: var(--text-medium);
            font-size: 16px;
        }

        .main-content {
            display: flex;
            gap: 20px;
        }

        .sidebar {
            width: 300px;
            background-color: var(--pocket-option-light);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .trading-interface {
            flex: 1;
            background-color: var(--pocket-option-light);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }

        .mode-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .mode-btn {
            flex: 1;
            padding: 10px;
            border: none;
            background-color: var(--medium-bg);
            color: var(--text-light);
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .mode-btn i {
            font-size: 20px;
        }

        .mode-btn.active {
            background-color: var(--primary-color);
        }

        .mode-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .balance-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background-color: var(--medium-bg);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .balance {
            font-weight: bold;
            font-size: 18px;
            color: var(--primary-color);
        }

        .balance-change {
            padding: 5px 10px;
            border-radius: 20px;
            background-color: var(--success-color);
            font-size: 14px;
            font-weight: bold;
        }

        .balance-change.negative {
            background-color: var(--danger-color);
        }

        .settings {
            margin-bottom: 20px;
        }

        .setting-group {
            margin-bottom: 15px;
            background-color: var(--medium-bg);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .setting-group label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--text-medium);
            font-weight: bold;
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .slider-container input {
            flex: 1;
        }

        .setting-group select {
            width: 100%;
            padding: 8px;
            background-color: var(--light-bg);
            border: 1px solid var(--border-color);
            color: var(--text-light);
            border-radius: 5px;
        }

        .trade-info {
            background-color: var(--medium-bg);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
        }

        .trade-info h3 {
            font-size: 16px;
            margin-top: 0;
            margin-bottom: 10px;
            color: var(--secondary-color);
        }

        .stats-row {
            display: flex;
            justify-content: space-between;
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat {
            flex: 1;
            text-align: center;
            background-color: var(--medium-bg);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-medium);
            margin-bottom: 5px;
        }

        .stat-value {
            font-weight: bold;
            font-size: 18px;
        }

        .buttons {
            display: flex;
            gap: 15px;
        }

        .action-btn {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            color: white;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .buy-btn {
            background-color: var(--success-color);
        }

        .buy-btn:hover {
            background-color: #0ca876;
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .sell-btn {
            background-color: var(--danger-color);
        }

        .sell-btn:hover {
            background-color: #dc3545;
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .auto-btn {
            background-color: var(--primary-color);
        }

        .auto-btn:hover {
            background-color: #3b78e7;
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .auto-btn.active {
            background-color: var(--warning-color);
        }

        .chart-container {
            flex: 1;
            background-color: var(--medium-bg);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            margin-bottom: 20px;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-medium);
            font-size: 18px;
        }

        footer {
            text-align: center;
            padding: 20px;
            color: var(--text-dark);
            font-size: 14px;
            border-top: 1px solid var(--border-color);
            margin-top: 30px;
        }

        .notification {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            background-color: var(--medium-bg);
            color: var(--text-light);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s, transform 0.3s;
        }

        .notification.show {
            opacity: 1;
            transform: translateY(0);
        }

        .notification.success {
            border-left: 4px solid var(--success-color);
        }

        .notification.error {
            border-left: 4px solid var(--danger-color);
        }

        .notification.info {
            border-left: 4px solid var(--primary-color);
        }

        .notification i {
            font-size: 20px;
        }

        .notification.success i {
            color: var(--success-color);
        }

        .notification.error i {
            color: var(--danger-color);
        }

        .notification.info i {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <header>
        <h1><i class="fas fa-robot"></i> Binary Options Trading Bot</h1>
        <div class="subtitle">Professional trading automation for Pocket Option</div>
    </header>

    <div class="container">
        <div class="main-content">
            <div class="trading-interface">
                <div class="mode-selector">
                    <button id="mode-quantum" class="mode-btn active">
                        <i class="fas fa-atom"></i>
                        <span>QUANTUM ADAPTIVE</span>
                    </button>
                    <button id="mode-phoenix" class="mode-btn">
                        <i class="fas fa-crown"></i>
                        <span>PHOENIX SYSTEM</span>
                    </button>
                    <button id="mode-neural" class="mode-btn">
                        <i class="fas fa-rocket"></i>
                        <span>NEURAL PULSE</span>
                    </button>
                </div>

                <div class="balance-row">
                    <div id="balance" class="balance">Balance: $1000.00</div>
                    <div id="balance-change" class="balance-change">+0.00%</div>
                </div>

                <div class="chart-container">
                    <div>Chart visualization will appear here when connected to Pocket Option</div>
                </div>

                <div class="stats-row">
                    <div class="stat">
                        <div class="stat-label">Win Rate</div>
                        <div class="stat-value" id="winRate">0.0%</div>
                    </div>
                    <div class="stat">
                        <div class="stat-label">Profit/Loss</div>
                        <div class="stat-value" id="profitLoss">0.0%</div>
                    </div>
                    <div class="stat">
                        <div class="stat-label">Total Trades</div>
                        <div class="stat-value" id="totalTrades">0</div>
                    </div>
                </div>

                <div class="buttons">
                    <button id="placeTrade" class="action-btn buy-btn">
                        <i class="fas fa-arrow-up"></i> BUY
                    </button>
                    <button id="placeSellTrade" class="action-btn sell-btn">
                        <i class="fas fa-arrow-down"></i> SELL
                    </button>
                    <button id="autoTrading" class="action-btn auto-btn">
                        <i class="fas fa-robot"></i> AUTO
                    </button>
                </div>
            </div>

            <div class="sidebar">
                <div class="settings">
                    <div class="setting-group">
                        <label for="riskPerTrade">Risk per Trade: <span id="riskValue">2.0%</span></label>
                        <div class="slider-container">
                            <input type="range" id="riskPerTrade" min="0.5" max="5" step="0.1" value="2">
                        </div>
                    </div>
                    
                    <div class="setting-group">
                        <label for="expiryTime">Expiry Time:</label>
                        <select id="expiryTime">
                            <option value="60">1 minute</option>
                            <option value="120">2 minutes</option>
                            <option value="300">5 minutes</option>
                        </select>
                    </div>
                </div>
                
                <div class="trade-info">
                    <h3>Current Trade</h3>
                    <div id="tradeInfo">No active trade</div>
                </div>

                <div class="setting-group">
                    <label for="pocketOptionUrl">Pocket Option URL:</label>
                    <input type="text" id="pocketOptionUrl" value="https://pocketoption.com/en/cabinet/demo-quick-high-low/" style="width: 100%; padding: 8px; background-color: var(--light-bg); border: 1px solid var(--border-color); color: var(--text-light); border-radius: 5px; margin-bottom: 10px;">
                    <button id="connectBtn" style="width: 100%; padding: 10px; background-color: var(--pocket-option-blue); color: white; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;">Connect to Pocket Option</button>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>Binary Options Trading Bot v1.0 | <i class="fas fa-exclamation-triangle"></i> For educational purposes only</p>
    </footer>

    <script src="standalone.js"></script>
</body>
</html>
