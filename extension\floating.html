<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KOS Trading Bot - Floating Mode</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="floating.css">
</head>
<body>
    <div class="floating-container" id="floatingContainer">
        <!-- Header with controls -->
        <div class="header">
            <h1><i class="fas fa-robot"></i> KOS Bot</h1>
            <div class="controls">
                <button id="lockBtn" class="control-btn" title="Lock/Unlock Position"><i class="fas fa-lock-open"></i></button>
                <button id="minimizeBtn" class="control-btn" title="Minimize"><i class="fas fa-minus"></i></button>
                <button id="closeBtn" class="control-btn" title="Close"><i class="fas fa-times"></i></button>
            </div>
        </div>

        <!-- Main content area - will be populated based on the selected mode -->
        <div class="content" id="botContent">
            <!-- Mode selector -->
            <div class="mode-selector">
                <button class="mode-btn active" id="mode-quantum">
                    <i class="fas fa-atom"></i> QUANTUM ADAPTIVE
                </button>
                <button class="mode-btn" id="mode-phoenix">
                    <i class="fas fa-crown"></i> PHOENIX SYSTEM
                </button>
                <button class="mode-btn" id="mode-neural">
                    <i class="fas fa-rocket"></i> NEURAL PULSE
                </button>
            </div>

            <!-- Mode-specific content will be inserted here -->
            <div id="mode-content" class="mode-content">
                <!-- This area will be dynamically populated based on the selected mode -->
            </div>
        </div>

        <!-- Minimized view -->
        <div class="minimized-content" id="minimizedContent">
            <i class="fas fa-robot"></i>
        </div>
    </div>

    <script src="floating.js"></script>
</body>
</html>
